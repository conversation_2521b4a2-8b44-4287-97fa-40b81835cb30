# EcoVolt - Electric Vehicle Charging Station Management System

A full-stack web application for managing electric vehicle charging stations built with Node.js, Express, PostgreSQL, and Vue.js.

## 🚀 Features

### Backend (Node.js + Express)
- **RESTful API** with comprehensive CRUD operations
- **JWT Authentication** with secure user registration and login
- **PostgreSQL Database** with optimized raw SQL queries
- **Protected Routes** with middleware authentication
- **Input Validation** and error handling
- **CORS Configuration** for cross-origin requests

### Frontend (Vue.js)
- **Modern Vue 3** with Composition API
- **Responsive Design** with Tailwind CSS
- **Dark/Light Theme** toggle functionality
- **State Management** with Pinia stores
- **Vue Router** with protected routes
- **Real-time API Integration** with Axios
- **Form Validation** and error handling

### Key Functionalities
- ✅ User registration and authentication
- ✅ Charging station CRUD operations
- ✅ Advanced filtering (status, power output, connector type)
- ✅ User ownership validation
- ✅ Responsive dashboard with statistics
- ✅ Profile management
- ✅ Theme customization
- 🔄 Map view (coming in Day 3)

## 🛠️ Tech Stack

**Backend:**
- Node.js & Express.js
- PostgreSQL (Render.com hosted)
- JWT for authentication
- bcryptjs for password hashing
- Raw SQL queries (no ORM)

**Frontend:**
- Vue.js 3 (Composition API)
- Tailwind CSS for styling
- Pinia for state management
- Vue Router for navigation
- Axios for API calls

## 📁 Project Structure

```
EcoVolt/
├── backend/                 # Node.js Express API
│   ├── config/             # Database configuration
│   ├── middleware/         # Authentication middleware
│   ├── models/            # Data models (User, ChargingStation)
│   ├── routes/            # API routes (auth, chargers)
│   ├── utils/             # Database utilities
│   ├── .env               # Environment variables
│   ├── app.js             # Main application file
│   └── package.json       # Backend dependencies
├── frontend/               # Vue.js Frontend
│   ├── src/
│   │   ├── components/    # Reusable Vue components
│   │   ├── views/         # Page components
│   │   ├── stores/        # Pinia state stores
│   │   ├── services/      # API service layer
│   │   ├── router/        # Vue Router configuration
│   │   └── assets/        # CSS and static assets
│   ├── tailwind.config.js # Tailwind CSS configuration
│   └── package.json       # Frontend dependencies
└── README.md              # Project documentation
```

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn
- PostgreSQL database (already configured with Render.com)

### Backend Setup

1. **Navigate to backend directory:**
   ```bash
   cd backend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Environment variables are already configured in `.env`:**
   - PostgreSQL connection to Render.com
   - JWT secret key
   - Server configuration

4. **Start the backend server:**
   ```bash
   npm start
   ```
   
   Server will run on `http://localhost:3000`

### Frontend Setup

1. **Navigate to frontend directory:**
   ```bash
   cd frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```
   
   Frontend will run on `http://localhost:5173`

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `POST /api/auth/refresh` - Refresh JWT token
- `GET /api/auth/users` - Get all users (protected)

### Charging Stations (Protected Routes)
- `GET /api/chargers` - List all charging stations (with filters)
- `GET /api/chargers/:id` - Get specific charging station
- `POST /api/chargers` - Create new charging station
- `PUT /api/chargers/:id` - Update charging station
- `DELETE /api/chargers/:id` - Delete charging station
- `GET /api/chargers/debug/all-data` - Debug endpoint for all data

### Health Check
- `GET /health` - Server health check
- `GET /` - API documentation

## 🎨 Frontend Features

### Pages
- **Login/Register** - User authentication with validation
- **Dashboard** - Charging stations overview with statistics
- **Charger Management** - Add, edit, delete charging stations
- **Map View** - Interactive map (placeholder for Day 3)
- **Profile** - User settings and account management

### Components
- **Navigation** - Responsive navbar with theme toggle
- **ChargerCard** - Individual charging station display
- **Theme Toggle** - Dark/light mode switching
- **Form Validation** - Real-time input validation

## 🔐 Authentication Flow

1. User registers/logs in through frontend
2. Backend validates credentials and returns JWT token
3. Token stored in localStorage and added to API requests
4. Protected routes check for valid token
5. Automatic logout on token expiration

## 📊 Database Schema

### Users Table
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Charging Stations Table
```sql
CREATE TABLE charging_stations (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  status VARCHAR(20) DEFAULT 'Active',
  power_output INTEGER NOT NULL,
  connector_type VARCHAR(50) NOT NULL,
  user_id INTEGER REFERENCES users(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🧪 Testing

### Backend Testing
Use the provided Postman collection (`backend/EcoVolt-API.postman_collection.json`) or test manually:

```bash
# Register user
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","password":"password123"}'

# Login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Create charging station (use token from login)
curl -X POST http://localhost:3000/api/chargers \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Station","latitude":40.7128,"longitude":-74.0060,"status":"Active","power_output":150,"connector_type":"Type 2"}'
```

### Frontend Testing
1. Open `http://localhost:5173`
2. Register a new account or use demo credentials
3. Test all CRUD operations on charging stations
4. Verify theme toggle and responsive design

## 🌟 Day 2 Completion Status

### ✅ Completed Features
- [x] Vue.js project setup with Tailwind CSS
- [x] Authentication system (login/register)
- [x] JWT token management in localStorage
- [x] Protected routes with navigation guards
- [x] Charging stations listing with filters
- [x] CRUD operations for charging stations
- [x] Responsive design with dark/light theme
- [x] State management with Pinia
- [x] API integration with error handling
- [x] Form validation and user feedback
- [x] Profile management
- [x] Statistics dashboard

### 🔄 Next Steps (Day 3)
- [ ] Interactive map integration (Google Maps/OpenStreetMap)
- [ ] Map markers for charging stations
- [ ] Click-to-view station details on map
- [ ] Enhanced filtering and search
- [ ] Real-time updates

## 🚀 Deployment Ready

The application is ready for deployment:
- **Backend**: Can be deployed to Render, Heroku, or AWS
- **Frontend**: Can be deployed to Vercel, Netlify, or Firebase Hosting
- **Database**: Already hosted on Render.com PostgreSQL

## 📝 Environment Variables

Backend `.env` file is pre-configured with:
```env
# Database (Render.com PostgreSQL)
DB_HOST=dpg-d0skslqdbo4c73f672c0-a.oregon-postgres.render.com
DB_PORT=5432
DB_USER=eco_volt_user
DB_PASS=tSMqWjKkZ8fr06MViYMPPers4XkNBfhu
DB_NAME=eco_volt

# JWT & Server
JWT_SECRET=Kke0odlU/EDSFvTWUPCbRGSlUUvijybeSHHO2bp82fI=
PORT=3000
NODE_ENV=development
```

## 🤝 Contributing

This is an internship assignment project. For any questions or issues, please refer to the assignment guidelines.

## 📄 License

This project is created for educational purposes as part of an internship assignment.
