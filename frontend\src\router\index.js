import { createRouter, createWebHistory } from 'vue-router'
import { isAuthenticated } from '@/services/api'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      redirect: '/chargers'
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/chargers',
      name: 'chargers',
      component: () => import('../views/ChargersView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/chargers/new',
      name: 'charger-create',
      component: () => import('../views/ChargerFormView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/chargers/:id/edit',
      name: 'charger-edit',
      component: () => import('../views/ChargerFormView.vue'),
      meta: { requiresAuth: true },
      props: true
    },
    {
      path: '/map',
      name: 'map',
      component: () => import('../views/MapView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/NotFoundView.vue')
    }
  ]
})

// Navigation guards
router.beforeEach((to, from, next) => {
  const authenticated = isAuthenticated()

  if (to.meta.requiresAuth && !authenticated) {
    next('/login')
  } else if (to.meta.requiresGuest && authenticated) {
    next('/chargers')
  } else {
    next()
  }
})

export default router
