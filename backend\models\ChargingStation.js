const { query } = require('../config/database');

class ChargingStation {
  constructor(id, name, latitude, longitude, status, power_output, connector_type, user_id, created_at, updated_at) {
    this.id = id;
    this.name = name;
    this.latitude = latitude;
    this.longitude = longitude;
    this.status = status;
    this.power_output = power_output;
    this.connector_type = connector_type;
    this.user_id = user_id;
    this.created_at = created_at;
    this.updated_at = updated_at;
  }

  // Create a new charging station
  static async create(stationData) {
    const { name, latitude, longitude, status, power_output, connector_type, user_id } = stationData;
    
    try {
      const result = await query(
        `INSERT INTO charging_stations (name, latitude, longitude, status, power_output, connector_type, user_id) 
         VALUES ($1, $2, $3, $4, $5, $6, $7) 
         RETURNING *`,
        [name, latitude, longitude, status || 'Active', power_output, connector_type, user_id]
      );

      const station = result.rows[0];
      return new ChargingStation(
        station.id,
        station.name,
        station.latitude,
        station.longitude,
        station.status,
        station.power_output,
        station.connector_type,
        station.user_id,
        station.created_at,
        station.updated_at
      );
    } catch (error) {
      throw error;
    }
  }

  // Get all charging stations
  static async findAll(filters = {}) {
    try {
      let queryText = 'SELECT * FROM charging_stations WHERE 1=1';
      const queryParams = [];
      let paramCount = 0;

      // Apply filters
      if (filters.status) {
        paramCount++;
        queryText += ` AND status = $${paramCount}`;
        queryParams.push(filters.status);
      }

      if (filters.power_output) {
        paramCount++;
        queryText += ` AND power_output >= $${paramCount}`;
        queryParams.push(filters.power_output);
      }

      if (filters.connector_type) {
        paramCount++;
        queryText += ` AND connector_type = $${paramCount}`;
        queryParams.push(filters.connector_type);
      }

      if (filters.user_id) {
        paramCount++;
        queryText += ` AND user_id = $${paramCount}`;
        queryParams.push(filters.user_id);
      }

      queryText += ' ORDER BY created_at DESC';

      const result = await query(queryText, queryParams);

      return result.rows.map(station => new ChargingStation(
        station.id,
        station.name,
        station.latitude,
        station.longitude,
        station.status,
        station.power_output,
        station.connector_type,
        station.user_id,
        station.created_at,
        station.updated_at
      ));
    } catch (error) {
      throw error;
    }
  }

  // Find charging station by ID
  static async findById(id) {
    try {
      const result = await query(
        'SELECT * FROM charging_stations WHERE id = $1',
        [id]
      );

      if (result.rows.length === 0) {
        return null;
      }

      const station = result.rows[0];
      return new ChargingStation(
        station.id,
        station.name,
        station.latitude,
        station.longitude,
        station.status,
        station.power_output,
        station.connector_type,
        station.user_id,
        station.created_at,
        station.updated_at
      );
    } catch (error) {
      throw error;
    }
  }

  // Update charging station
  static async update(id, updateData) {
    try {
      const { name, latitude, longitude, status, power_output, connector_type } = updateData;
      
      const result = await query(
        `UPDATE charging_stations 
         SET name = $1, latitude = $2, longitude = $3, status = $4, 
             power_output = $5, connector_type = $6, updated_at = CURRENT_TIMESTAMP
         WHERE id = $7 
         RETURNING *`,
        [name, latitude, longitude, status, power_output, connector_type, id]
      );

      if (result.rows.length === 0) {
        return null;
      }

      const station = result.rows[0];
      return new ChargingStation(
        station.id,
        station.name,
        station.latitude,
        station.longitude,
        station.status,
        station.power_output,
        station.connector_type,
        station.user_id,
        station.created_at,
        station.updated_at
      );
    } catch (error) {
      throw error;
    }
  }

  // Delete charging station
  static async delete(id) {
    try {
      const result = await query(
        'DELETE FROM charging_stations WHERE id = $1 RETURNING *',
        [id]
      );

      return result.rows.length > 0;
    } catch (error) {
      throw error;
    }
  }

  // Convert to JSON
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      latitude: parseFloat(this.latitude),
      longitude: parseFloat(this.longitude),
      status: this.status,
      power_output: this.power_output,
      connector_type: this.connector_type,
      user_id: this.user_id,
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }
}

module.exports = ChargingStation;
