<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Charging Stations Map</h1>
        <p class="mt-2 text-gray-600 dark:text-gray-400">
          View all charging stations on an interactive map
        </p>
      </div>
    </div>

    <!-- Map Container -->
    <div class="card p-6">
      <div class="bg-gray-100 dark:bg-gray-700 rounded-lg h-96 flex items-center justify-center">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7"/>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Map View Coming Soon</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Interactive map with charging station markers will be implemented in Day 3
          </p>
        </div>
      </div>
    </div>

    <!-- Stations List -->
    <div class="card p-6">
      <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Charging Stations ({{ chargersStore.chargers.length }})
      </h2>
      
      <div v-if="chargersStore.loading" class="flex justify-center py-8">
        <div class="spinner w-6 h-6"></div>
      </div>
      
      <div v-else-if="chargersStore.chargers.length === 0" class="text-center py-8">
        <p class="text-gray-500 dark:text-gray-400">No charging stations found</p>
      </div>
      
      <div v-else class="space-y-3">
        <div 
          v-for="charger in chargersStore.chargers" 
          :key="charger.id"
          class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
        >
          <div class="flex-1">
            <h3 class="font-medium text-gray-900 dark:text-white">{{ charger.name }}</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ charger.latitude }}, {{ charger.longitude }} • {{ charger.power_output }}kW • {{ charger.connector_type }}
            </p>
          </div>
          <div class="flex items-center space-x-2">
            <span 
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
              :class="charger.status === 'Active' 
                ? 'bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200'
                : 'bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200'"
            >
              {{ charger.status }}
            </span>
            <router-link 
              :to="`/chargers/${charger.id}/edit`"
              class="btn btn-secondary btn-sm"
            >
              Edit
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useChargersStore } from '@/stores/chargers'

const chargersStore = useChargersStore()

onMounted(() => {
  chargersStore.fetchChargers()
})
</script>
