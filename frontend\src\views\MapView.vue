<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
      <div class="flex-1">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          🗺️ Charging Stations Map
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Interactive map showing all charging stations with real-time status
        </p>
      </div>
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600 dark:text-gray-400">Total Stations:</span>
          <span class="font-semibold text-primary-600 dark:text-primary-400">{{ chargersStore.chargers.length }}</span>
        </div>
        <router-link to="/chargers/new" class="btn btn-primary">
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
          </svg>
          Add Station
        </router-link>
      </div>
    </div>

    <!-- Filters -->
    <div class="card p-4">
      <div class="flex flex-wrap items-center gap-4">
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Filter:</label>
        </div>

        <select v-model="filters.status" @change="applyFilters" class="input-sm">
          <option value="">All Status</option>
          <option value="Active">Active Only</option>
          <option value="Inactive">Inactive Only</option>
        </select>

        <select v-model="filters.connector_type" @change="applyFilters" class="input-sm">
          <option value="">All Connectors</option>
          <option value="Type 1">Type 1</option>
          <option value="Type 2">Type 2</option>
          <option value="CCS">CCS</option>
          <option value="CHAdeMO">CHAdeMO</option>
        </select>

        <input
          v-model="filters.power_output"
          @input="applyFilters"
          type="number"
          placeholder="Min Power (kW)"
          class="input-sm w-32"
        />

        <button @click="clearFilters" class="btn btn-secondary btn-sm">
          Clear Filters
        </button>

        <button @click="centerMap" class="btn btn-secondary btn-sm">
          <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
          </svg>
          Center Map
        </button>
      </div>
    </div>

    <!-- Map Container -->
    <div class="card overflow-hidden">
      <div class="relative">
        <div
          ref="mapContainer"
          class="w-full h-96 lg:h-[500px]"
          :class="{ 'opacity-50': chargersStore.loading }"
        ></div>

        <!-- Loading Overlay -->
        <div v-if="chargersStore.loading || mapLoading" class="absolute inset-0 bg-white dark:bg-gray-800 bg-opacity-75 flex items-center justify-center">
          <div class="text-center">
            <div class="spinner w-8 h-8 mx-auto mb-2"></div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Loading map and stations...</p>
          </div>
        </div>

        <!-- Map Controls -->
        <div class="absolute top-4 right-4 space-y-2">
          <button
            @click="toggleMapType"
            class="bg-white dark:bg-gray-800 p-2 rounded-lg shadow-lg hover:shadow-xl transition-shadow"
            title="Toggle Map Type"
          >
            <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Station Info Panel -->
    <div v-if="selectedStation" class="card p-6 border-l-4 border-primary-500">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {{ selectedStation.name }}
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span class="text-gray-500 dark:text-gray-400">Status:</span>
              <span
                class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                :class="selectedStation.status === 'Active'
                  ? 'bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200'
                  : 'bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200'"
              >
                {{ selectedStation.status }}
              </span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">Power:</span>
              <span class="ml-2 font-medium">{{ selectedStation.power_output }}kW</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">Connector:</span>
              <span class="ml-2 font-medium">{{ selectedStation.connector_type }}</span>
            </div>
          </div>
          <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            📍 {{ selectedStation.latitude }}, {{ selectedStation.longitude }}
          </div>
        </div>
        <div class="flex space-x-2">
          <router-link
            :to="`/chargers/${selectedStation.id}/edit`"
            class="btn btn-secondary btn-sm"
          >
            Edit
          </router-link>
          <button @click="selectedStation = null" class="btn btn-secondary btn-sm">
            Close
          </button>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div class="card p-4 text-center">
        <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">{{ activeStations }}</div>
        <div class="text-sm text-gray-500 dark:text-gray-400">Active Stations</div>
      </div>
      <div class="card p-4 text-center">
        <div class="text-2xl font-bold text-warning-600 dark:text-warning-400">{{ inactiveStations }}</div>
        <div class="text-sm text-gray-500 dark:text-gray-400">Inactive Stations</div>
      </div>
      <div class="card p-4 text-center">
        <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ averagePower }}kW</div>
        <div class="text-sm text-gray-500 dark:text-gray-400">Avg Power</div>
      </div>
      <div class="card p-4 text-center">
        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ filteredStations.length }}</div>
        <div class="text-sm text-gray-500 dark:text-gray-400">Visible on Map</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { useChargersStore } from '@/stores/chargers'

const route = useRoute()
const chargersStore = useChargersStore()

// Reactive data
const mapContainer = ref(null)
const map = ref(null)
const markers = ref([])
const selectedStation = ref(null)
const mapLoading = ref(true)
const currentMapType = ref('roadmap')

// Filters
const filters = ref({
  status: '',
  connector_type: '',
  power_output: ''
})

// Computed properties
const filteredStations = computed(() => {
  let stations = chargersStore.chargers

  if (filters.value.status) {
    stations = stations.filter(s => s.status === filters.value.status)
  }

  if (filters.value.connector_type) {
    stations = stations.filter(s => s.connector_type === filters.value.connector_type)
  }

  if (filters.value.power_output) {
    stations = stations.filter(s => s.power_output >= parseInt(filters.value.power_output))
  }

  return stations
})

const activeStations = computed(() =>
  chargersStore.chargers.filter(s => s.status === 'Active').length
)

const inactiveStations = computed(() =>
  chargersStore.chargers.filter(s => s.status === 'Inactive').length
)

const averagePower = computed(() => {
  if (chargersStore.chargers.length === 0) return 0
  const total = chargersStore.chargers.reduce((sum, s) => sum + s.power_output, 0)
  return Math.round(total / chargersStore.chargers.length)
})

// Google Maps functions
const initializeMap = async () => {
  if (!window.google) {
    console.error('Google Maps API not loaded')
    mapLoading.value = false
    return
  }

  try {
    // Default center (you can change this to your preferred location)
    const defaultCenter = { lat: 40.7128, lng: -74.0060 } // New York City

    // Check if there's a specific station to focus on from route query
    let center = defaultCenter
    if (route.query.lat && route.query.lng) {
      center = {
        lat: parseFloat(route.query.lat),
        lng: parseFloat(route.query.lng)
      }
    } else if (chargersStore.chargers.length > 0) {
      // Center on first station if available
      const firstStation = chargersStore.chargers[0]
      center = {
        lat: parseFloat(firstStation.latitude),
        lng: parseFloat(firstStation.longitude)
      }
    }

    map.value = new google.maps.Map(mapContainer.value, {
      zoom: 10,
      center: center,
      mapTypeId: currentMapType.value,
      styles: [
        {
          featureType: 'poi',
          elementType: 'labels',
          stylers: [{ visibility: 'off' }]
        }
      ],
      mapTypeControl: true,
      streetViewControl: true,
      fullscreenControl: true,
      zoomControl: true
    })

    // Add markers for all stations
    addMarkersToMap()

    mapLoading.value = false
  } catch (error) {
    console.error('Error initializing map:', error)
    mapLoading.value = false
  }
}

const addMarkersToMap = () => {
  if (!map.value) return

  // Clear existing markers
  clearMarkers()

  // Add markers for filtered stations
  filteredStations.value.forEach(station => {
    const marker = new google.maps.Marker({
      position: {
        lat: parseFloat(station.latitude),
        lng: parseFloat(station.longitude)
      },
      map: map.value,
      title: station.name,
      icon: {
        url: getMarkerIcon(station.status),
        scaledSize: new google.maps.Size(40, 40),
        origin: new google.maps.Point(0, 0),
        anchor: new google.maps.Point(20, 40)
      }
    })

    // Create info window content
    const infoWindowContent = `
      <div class="p-3 max-w-xs">
        <h3 class="font-semibold text-lg mb-2">${station.name}</h3>
        <div class="space-y-1 text-sm">
          <div class="flex justify-between">
            <span class="text-gray-600">Status:</span>
            <span class="font-medium ${station.status === 'Active' ? 'text-green-600' : 'text-yellow-600'}">
              ${station.status}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Power:</span>
            <span class="font-medium">${station.power_output}kW</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Connector:</span>
            <span class="font-medium">${station.connector_type}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Owner:</span>
            <span class="font-medium">${station.owner_name || 'Unknown'}</span>
          </div>
        </div>
        <div class="mt-3 pt-2 border-t">
          <button
            onclick="window.selectStation(${station.id})"
            class="w-full bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600 transition-colors"
          >
            View Details
          </button>
        </div>
      </div>
    `

    const infoWindow = new google.maps.InfoWindow({
      content: infoWindowContent
    })

    marker.addListener('click', () => {
      // Close other info windows
      markers.value.forEach(m => {
        if (m.infoWindow) {
          m.infoWindow.close()
        }
      })

      infoWindow.open(map.value, marker)
    })

    markers.value.push({
      marker,
      infoWindow,
      station
    })
  })

  // Focus on specific station if provided in route
  if (route.query.id) {
    const stationId = parseInt(route.query.id)
    const markerData = markers.value.find(m => m.station.id === stationId)
    if (markerData) {
      map.value.setCenter(markerData.marker.getPosition())
      map.value.setZoom(15)
      markerData.infoWindow.open(map.value, markerData.marker)
    }
  }
}

const getMarkerIcon = (status) => {
  // Create custom marker icons based on status
  const color = status === 'Active' ? '22c55e' : 'f59e0b' // Green for active, yellow for inactive
  return `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
    <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
      <circle cx="20" cy="20" r="18" fill="#${color}" stroke="#ffffff" stroke-width="2"/>
      <text x="20" y="26" text-anchor="middle" fill="white" font-family="Arial" font-size="16" font-weight="bold">⚡</text>
    </svg>
  `)}`
}

const clearMarkers = () => {
  markers.value.forEach(markerData => {
    markerData.marker.setMap(null)
    if (markerData.infoWindow) {
      markerData.infoWindow.close()
    }
  })
  markers.value = []
}

// Filter functions
const applyFilters = () => {
  nextTick(() => {
    addMarkersToMap()
  })
}

const clearFilters = () => {
  filters.value = {
    status: '',
    connector_type: '',
    power_output: ''
  }
  applyFilters()
}

// Map control functions
const centerMap = () => {
  if (!map.value || filteredStations.value.length === 0) return

  const bounds = new google.maps.LatLngBounds()
  filteredStations.value.forEach(station => {
    bounds.extend({
      lat: parseFloat(station.latitude),
      lng: parseFloat(station.longitude)
    })
  })

  map.value.fitBounds(bounds)
}

const toggleMapType = () => {
  if (!map.value) return

  const types = ['roadmap', 'satellite', 'hybrid', 'terrain']
  const currentIndex = types.indexOf(currentMapType.value)
  const nextIndex = (currentIndex + 1) % types.length
  currentMapType.value = types[nextIndex]

  map.value.setMapTypeId(currentMapType.value)
}

// Global function for info window buttons
window.selectStation = (stationId) => {
  const station = chargersStore.chargers.find(s => s.id === stationId)
  if (station) {
    selectedStation.value = station
  }
}

// Watch for changes in chargers data
watch(() => chargersStore.chargers, () => {
  if (map.value) {
    addMarkersToMap()
  }
}, { deep: true })

// Lifecycle hooks
onMounted(async () => {
  await chargersStore.fetchChargers()

  // Wait for Google Maps API to load
  const checkGoogleMaps = () => {
    if (window.google && window.google.maps) {
      initializeMap()
    } else {
      setTimeout(checkGoogleMaps, 100)
    }
  }

  checkGoogleMaps()
})

onUnmounted(() => {
  // Clean up
  clearMarkers()
  if (window.selectStation) {
    delete window.selectStation
  }
})
</script>
