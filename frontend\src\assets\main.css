@import './base.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for Theme */
:root {
  --color-primary: #3b82f6;
  --color-primary-dark: #1d4ed8;
  --color-success: #22c55e;
  --color-warning: #f59e0b;
  --color-danger: #ef4444;
}

/* Dark mode variables */
.dark {
  --color-bg: #111827;
  --color-surface: #1f2937;
  --color-text: #f9fafb;
  --color-text-secondary: #d1d5db;
}

/* Light mode variables */
.light {
  --color-bg: #f9fafb;
  --color-surface: #ffffff;
  --color-text: #111827;
  --color-text-secondary: #6b7280;
}

/* Custom component styles */
.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 transform hover:scale-105 active:scale-95 relative overflow-hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  @apply bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700 focus:ring-primary-500 shadow-lg hover:shadow-xl;
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.btn-primary:hover {
  @apply shadow-primary-500/50;
  box-shadow: 0 10px 25px -5px rgba(34, 197, 94, 0.4), 0 10px 10px -5px rgba(34, 197, 94, 0.04);
}

.btn-secondary {
  @apply bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-2 border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-primary-500 shadow-md hover:shadow-lg;
}

.btn-success {
  @apply bg-gradient-to-r from-success-500 to-success-600 text-white hover:from-success-600 hover:to-success-700 focus:ring-success-500 shadow-lg hover:shadow-xl;
}

.btn-danger {
  @apply bg-gradient-to-r from-danger-500 to-danger-600 text-white hover:from-danger-600 hover:to-danger-700 focus:ring-danger-500 shadow-lg hover:shadow-xl;
}

.btn-sm {
  @apply px-3 py-1.5 text-sm;
}

.btn-lg {
  @apply px-6 py-3 text-lg;
}

.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700;
}

.input {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100;
}

.input-sm {
  @apply px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100;
}

.label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
}

/* Transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500 dark:bg-gray-500;
}

/* Leaflet Map Styles */
.leaflet-container {
  @apply rounded-lg;
}

.leaflet-popup-content-wrapper {
  @apply rounded-lg shadow-xl border-0;
}

.leaflet-popup-content {
  margin: 0 !important;
}

.leaflet-popup-tip {
  @apply shadow-lg;
}

.custom-marker {
  background: transparent !important;
  border: none !important;
}

.custom-popup .leaflet-popup-content-wrapper {
  @apply bg-white rounded-lg shadow-2xl;
}

.custom-popup .leaflet-popup-tip {
  @apply bg-white;
}

/* Dark theme support for map */
.dark .leaflet-popup-content-wrapper {
  @apply bg-gray-800 text-white;
}

.dark .leaflet-popup-tip {
  @apply bg-gray-800;
}

.dark .custom-popup .leaflet-popup-content-wrapper {
  @apply bg-gray-800;
}

.dark .custom-popup .leaflet-popup-tip {
  @apply bg-gray-800;
}