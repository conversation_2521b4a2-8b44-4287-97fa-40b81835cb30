# EcoVolt Backend API

A Node.js Express backend for managing electric vehicle charging stations with user authentication.

## Features

- **User Authentication**: JWT-based authentication with register/login
- **Charging Station Management**: Full CRUD operations for charging stations
- **PostgreSQL Database**: Raw SQL queries for optimal performance
- **Protected Routes**: JWT middleware for secure endpoints
- **Input Validation**: Comprehensive validation for all endpoints
- **Error Handling**: Proper error responses and logging

## Tech Stack

- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **PostgreSQL** - Database
- **JWT** - Authentication
- **bcryptjs** - Password hashing
- **CORS** - Cross-origin resource sharing

## Setup Instructions

### Prerequisites

- Node.js (v14 or higher)
- PostgreSQL database (Render.com hosted)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   
   The `.env` file is already configured with Render.com PostgreSQL credentials:
   ```env
   DB_HOST=dpg-d0skslqdbo4c73f672c0-a.oregon-postgres.render.com
   DB_PORT=5432
   DB_USER=eco_volt_user
   DB_PASS=tSMqWjKkZ8fr06MViYMPPers4XkNBfhu
   DB_NAME=eco_volt
   DATABASE_URL=postgresql://eco_volt_user:<EMAIL>/eco_volt
   JWT_SECRET=Kke0odlU/EDSFvTWUPCbRGSlUUvijybeSHHO2bp82fI=
   PORT=3000
   NODE_ENV=development
   ```

4. **Start the server**
   ```bash
   npm start
   ```

   The server will start on `http://localhost:3000`

## API Endpoints

### Authentication Routes

#### Register User
- **POST** `/api/auth/register`
- **Body**: 
  ```json
  {
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```

#### Login User
- **POST** `/api/auth/login`
- **Body**: 
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```

#### Get Current User
- **GET** `/api/auth/me`
- **Headers**: `Authorization: Bearer <token>`

### Charging Station Routes (Protected)

All charging station routes require authentication header: `Authorization: Bearer <token>`

#### Get All Charging Stations
- **GET** `/api/chargers`
- **Query Parameters** (optional):
  - `status`: Active/Inactive
  - `power_output`: Minimum power output
  - `connector_type`: Connector type filter

#### Get Charging Station by ID
- **GET** `/api/chargers/:id`

#### Create Charging Station
- **POST** `/api/chargers`
- **Body**: 
  ```json
  {
    "name": "Station Name",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "status": "Active",
    "power_output": 150,
    "connector_type": "Type 2"
  }
  ```

#### Update Charging Station
- **PUT** `/api/chargers/:id`
- **Body**: Same as create

#### Delete Charging Station
- **DELETE** `/api/chargers/:id`

## Database Schema

### Users Table
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Charging Stations Table
```sql
CREATE TABLE charging_stations (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  status VARCHAR(20) DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
  power_output INTEGER NOT NULL,
  connector_type VARCHAR(50) NOT NULL,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Testing with Postman

1. **Register a user** using `/api/auth/register`
2. **Login** using `/api/auth/login` to get JWT token
3. **Use the token** in Authorization header for protected routes
4. **Test CRUD operations** on charging stations

## Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "message": "Error description"
}
```

## Success Responses

All successful responses follow this format:

```json
{
  "success": true,
  "message": "Operation description",
  "data": {
    // Response data
  }
}
```

## Development

- Database tables are automatically created on server start
- All database operations use raw SQL for optimal performance
- JWT tokens expire in 7 days
- Passwords are hashed using bcryptjs with salt rounds of 10

## Security Features

- Password hashing with bcryptjs
- JWT token authentication
- Input validation and sanitization
- SQL injection prevention with parameterized queries
- CORS configuration for cross-origin requests
